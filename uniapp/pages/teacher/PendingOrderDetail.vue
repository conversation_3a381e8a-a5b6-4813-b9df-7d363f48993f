<template>
  <view class="order-detail">
    <!-- 顶部导航栏 -->
    <view class="nav-bar">
      <text class="back" @click="goBack">＜</text>
      <text class="nav-title">订单详情</text>
      <view class="nav-actions">
        <text class="nav-icon">⋯</text>
        <text class="nav-icon">⟲</text>
      </view>
    </view>

    <!-- 订单详情内容 -->
    <scroll-view class="content" scroll-y="true" :style="{ height: scrollHeight + 'px' }">
      <!-- 基本信息列表 -->
      <view class="info-list">
        <view class="info-item">
          <text class="info-label">订单号</text>
          <text class="info-value">{{ order.no || 'FD13455555333' }}</text>
        </view>

        <view class="info-item">
          <text class="info-label">发布时间</text>
          <text class="info-value">{{ order.date || '2025-3-12 12:11:12' }}</text>
        </view>

        <view class="info-item">
          <text class="info-label">辅导项目</text>
          <text class="info-value">{{ order.type || '作业' }}</text>
        </view>

        <view class="info-item">
          <text class="info-label">学习阶段</text>
          <text class="info-value">{{ order.stage || '本科' }}</text>
        </view>

        <view class="info-item">
          <text class="info-label">学习位置</text>
          <text class="info-value">{{ order.location || '国内' }}</text>
        </view>

        <view class="info-item">
          <text class="info-label">学习专业</text>
          <text class="info-value">{{ order.major || '会计' }}</text>
        </view>

        <view class="info-item">
          <text class="info-label">预算区间</text>
          <text class="info-value budget">{{ order.budget || '1000-2000元' }}</text>
        </view>

        <view class="info-item">
          <text class="info-label">老师要求</text>
          <text class="info-value"></text>
        </view>

        <view class="info-item">
          <text class="info-label">老师情况</text>
          <view class="teacher-tags">
            <text class="tag-btn active">博士</text>
          </view>
        </view>

        <view class="info-item">
          <text class="info-label">是否留学</text>
          <view class="study-abroad">
            <text class="tag-btn inactive">不要求</text>
          </view>
        </view>

        <view class="info-item">
          <text class="info-label">需求描述</text>
          <text class="info-value"></text>
        </view>
      </view>

      <!-- 详细描述 -->
      <view class="description-section">
        <text class="description-text">{{ order.description || '想要找一个辅导论文的老师，想要找一个辅导论文的老师想要找一个辅导论文的老师想要找一个辅导论文的老师想要找一个辅导论文的老师想要找一个辅导论文的老师想要找一个辅导论文的老师' }}</text>
      </view>

      <!-- 其他说明 -->
      <view class="other-section">
        <view class="section-title">
          <text class="title-text">其他说明</text>
        </view>
        <text class="other-text">{{ order.extra || '希望老师热情专业，有耐心希望老师热情专业，有耐心希望老师热情专业，有耐心希望老师热情专业，有耐心希望老师热情专业，有耐心希望老师热情专业，有耐心。' }}</text>
      </view>
    </scroll-view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { onLoad } from '@dcloudio/uni-app'

const scrollHeight = ref(600)

// 订单信息
const order = ref({
  id: '',
  no: '',
  date: '',
  type: '',
  stage: '',
  location: '',
  major: '',
  budget: '',
  description: '',
  extra: ''
})

// 返回上一页
const goBack = () => {
  uni.navigateBack()
}

onLoad((options) => {
  if (options && options.order) {
    // 接收传递的订单数据
    const orderData = JSON.parse(decodeURIComponent(options.order))
    order.value = { ...order.value, ...orderData }
  }
})

onMounted(() => {
  // 计算滚动区域高度
  const systemInfo = uni.getSystemInfoSync()
  scrollHeight.value = systemInfo.windowHeight - 120 // 减去导航栏高度
})
</script>

<style scoped>
.order-detail {
  min-height: calc(100vh - 120px);
  background: #f8f8f8;
  display: flex;
  flex-direction: column;
}

/* 顶部导航栏 */
.nav-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 32rpx;
  background: #fff;
  border-bottom: 1rpx solid #eee;
}

.back {
  font-size: 36rpx;
  color: #333;
  padding: 0 16rpx;
}

.nav-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  flex: 1;
  text-align: center;
}

.nav-actions {
  display: flex;
  gap: 16rpx;
}

.nav-icon {
  font-size: 32rpx;
  color: #666;
  padding: 0 8rpx;
}

/* 内容区域 */
.content {
  flex: 1;
  background: #fff;
}

/* 信息列表 */
.info-list {
  padding: 0 32rpx;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
  min-height: 60rpx;
}

.info-item:last-child {
  border-bottom: none;
}

.info-label {
  font-size: 30rpx;
  color: #333;
  width: 160rpx;
  flex-shrink: 0;
}

.info-value {
  font-size: 30rpx;
  color: #333;
  flex: 1;
  text-align: right;
}

.info-value.budget {
  color: #333;
}

/* 标签按钮 */
.teacher-tags, .study-abroad {
  display: flex;
  justify-content: flex-end;
}

.tag-btn {
  padding: 8rpx 20rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  border: 1rpx solid #ddd;
}

.tag-btn.active {
  background: #1e98d7;
  color: #fff;
  border-color: #1e98d7;
}

.tag-btn.inactive {
  background: #f8f8f8;
  color: #666;
  border-color: #ddd;
}

/* 描述区域 */
.description-section {
  padding: 32rpx;
  border-bottom: 20rpx solid #f8f8f8;
}

.description-text {
  font-size: 28rpx;
  color: #333;
  line-height: 1.6;
}

/* 其他说明 */
.other-section {
  padding: 32rpx;
}

.section-title {
  margin-bottom: 20rpx;
}

.title-text {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
}

.other-text {
  font-size: 28rpx;
  color: #333;
  line-height: 1.6;
}
</style>
