<template>
  <view class="teacher-home">
    <!-- 顶部导航栏 -->
    <view class="nav-bar">
      <image class="nav-logo" src="/static/logo.png" mode="aspectFit" />
      <text class="nav-title">辅导君</text>
      <view class="nav-actions">
        <text class="nav-icon">⋯</text>
        <text class="nav-icon">⟲</text>
      </view>
    </view>

    <!-- 山峰背景图 -->
    <view class="header-bg">
      <view class="mountain-bg">
        <view class="mountain mountain1"></view>
        <view class="mountain mountain2"></view>
        <view class="mountain mountain3"></view>
      </view>
    </view>

    <!-- 导航标签 -->
    <view class="nav-tabs">
      <text
        class="tab-item"
        :class="{ active: activeTab === 'recommend' }"
        @click="switchTab('recommend')"
      >推荐</text>
      <text
        class="tab-item"
        :class="{ active: activeTab === 'thesis' }"
        @click="switchTab('thesis')"
      >论文辅导</text>
      <text
        class="tab-item"
        :class="{ active: activeTab === 'homework' }"
        @click="switchTab('homework')"
      >作业辅导</text>
      <text
        class="tab-item"
        :class="{ active: activeTab === 'postgrad' }"
        @click="switchTab('postgrad')"
      >保研</text>
    </view>

    <!-- 订单列表 -->
    <scroll-view class="order-list" scroll-y="true" :style="{ height: scrollHeight + 'px' }">
      <view class="order-card" v-for="(order, index) in orders" :key="index" @click="goOrderDetail(order)">
        <view class="order-header">
          <image class="student-avatar" :src="order.studentAvatar" mode="aspectFill"></image>
          <view class="order-info">
            <text class="student-name">{{ order.studentName }}</text>
            <text class="order-time">{{ order.publishTime }}</text>
          </view>
          <view class="order-status" :class="order.status">
            <text class="status-text">{{ getStatusText(order.status) }}</text>
          </view>
        </view>
        
        <view class="order-content">
          <text class="order-title">{{ order.title }}</text>
          <text class="order-desc">{{ order.description }}</text>
          <view class="order-tags">
            <text class="tag" v-for="tag in order.tags" :key="tag">{{ tag }}</text>
          </view>
        </view>

        <view class="order-footer">
          <text class="order-price">预算：{{ order.budget }}</text>
          <view class="order-actions">
            <view v-if="order.status === 'pending'" class="action-btn accept-btn" @click.stop="acceptOrder(order)">
              <text class="btn-text">接单</text>
            </view>
            <view v-if="order.status === 'accepted'" class="action-btn contact-btn" @click.stop="contactStudent(order)">
              <text class="btn-text">联系学员</text>
            </view>
            <view v-if="order.status === 'completed'" class="action-btn completed-btn">
              <text class="btn-text">已完成</text>
            </view>
          </view>
        </view>
      </view>
    </scroll-view>



    <!-- 底部导航 -->
    <view class="bottom-nav">
      <view class="nav-item active" @click="goToPage('home')">
        <text class="nav-icon">🏠</text>
        <text class="nav-text">首页</text>
      </view>
      <view class="nav-item" @click="goToPage('message')">
        <text class="nav-icon">📋</text>
        <text class="nav-text">消息</text>
      </view>
      <view class="nav-item" @click="goToPage('profile')">
        <text class="nav-icon">👤</text>
        <text class="nav-text">我的</text>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'

const scrollHeight = ref(600)
const activeTab = ref('recommend')

// 所有订单数据
const allOrders = ref([
  {
    id: '1',
    studentName: '张伟｜本科｜心理学',
    studentAvatar: '/static/logo.png',
    publishTime: '2025-3-12 11:12',
    status: 'pending',
    title: '需要本科论文辅导',
    description: '需要本科论文辅导需要本科论文辅导需要本科论文辅导需要本科论文辅导需要本科论文辅导需要本科论文辅导需要本科论文辅导需要本科论文辅导',
    tags: ['博士', '硕士', '博士'],
    budget: '10000-20000',
    category: 'thesis'
  },
  {
    id: '2',
    studentName: '李明｜硕士｜计算机',
    studentAvatar: '/static/logo.png',
    publishTime: '2025-3-12 10:30',
    status: 'accepted',
    title: '硕士论文指导',
    description: '需要计算机专业硕士论文指导，主要是算法优化方面的内容',
    tags: ['计算机', '算法', '硕士'],
    budget: '15000-25000',
    category: 'thesis'
  },
  {
    id: '3',
    studentName: '王芳｜本科｜数学',
    studentAvatar: '/static/logo.png',
    publishTime: '2025-3-12 09:15',
    status: 'pending',
    title: '高等数学作业辅导',
    description: '需要高等数学作业辅导，主要是微积分和线性代数部分',
    tags: ['数学', '微积分', '线性代数'],
    budget: '5000-8000',
    category: 'homework'
  },
  {
    id: '4',
    studentName: '陈强｜本科｜经济学',
    studentAvatar: '/static/logo.png',
    publishTime: '2025-3-12 08:45',
    status: 'pending',
    title: '保研面试指导',
    description: '需要保研面试指导，包括简历修改和面试技巧培训',
    tags: ['保研', '面试', '简历'],
    budget: '3000-5000',
    category: 'postgrad'
  }
])

// 根据当前标签过滤订单
const orders = computed(() => {
  if (activeTab.value === 'recommend') {
    return allOrders.value
  }
  return allOrders.value.filter(order => order.category === activeTab.value)
})

// 切换标签
const switchTab = (tab) => {
  activeTab.value = tab
}

// 底部导航跳转
const goToPage = (page) => {
  switch (page) {
    case 'home':
      // 当前页面，不需要跳转
      break
    case 'message':
      uni.navigateTo({
        url: '/uniapp/pages/teacher/Message'
      })
      break
    case 'profile':
      uni.navigateTo({
        url: '/uniapp/pages/teacher/Profile'
      })
      break
  }
}

const getStatusText = (status) => {
  const statusMap = {
    pending: '已有20人接单',
    accepted: '作业进度中',
    completed: '已完成'
  }
  return statusMap[status] || '未知状态'
}

const goOrderDetail = (order) => {
  console.log('查看订单详情:', order)
  // 跳转到订单详情页面
  uni.navigateTo({
    url: '/uniapp/pages/teacher/OrderDetail?order=' + encodeURIComponent(JSON.stringify(order))
  })
}

const acceptOrder = (order) => {
  console.log('接单:', order)
  uni.showToast({
    title: '接单成功',
    icon: 'success'
  })
  order.status = 'accepted'
}

const contactStudent = (order) => {
  console.log('联系学员:', order)
  uni.showToast({
    title: '已发送联系信息',
    icon: 'success'
  })
}

onMounted(() => {
  // 计算滚动区域高度
  const systemInfo = uni.getSystemInfoSync()
  scrollHeight.value = systemInfo.windowHeight - 400 // 减去导航栏、头部和底部导航高度
})
</script>

<style scoped>
.teacher-home {
  min-height: calc(100vh - 120px);
  background: #f8f8f8;
  display: flex;
  flex-direction: column;
}

/* 顶部导航栏 */
.nav-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 32rpx;
  background: #fff;
  border-bottom: 1rpx solid #eee;
}

.nav-logo {
  width: 48rpx;
  height: 48rpx;
}

.nav-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  flex: 1;
  text-align: center;
}

.nav-actions {
  display: flex;
  gap: 16rpx;
}

.nav-icon {
  font-size: 36rpx;
  color: #333;
  padding: 0 8rpx;
}

/* 山峰背景 */
.header-bg {
  height: 200rpx;
  background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
  position: relative;
  overflow: hidden;
}

.mountain-bg {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 120rpx;
}

.mountain {
  position: absolute;
  bottom: 0;
}

.mountain1 {
  left: 0;
  width: 0;
  height: 0;
  border-left: 200rpx solid transparent;
  border-right: 200rpx solid transparent;
  border-bottom: 80rpx solid #0984e3;
}

.mountain2 {
  left: 300rpx;
  width: 0;
  height: 0;
  border-left: 150rpx solid transparent;
  border-right: 150rpx solid transparent;
  border-bottom: 100rpx solid #74b9ff;
}

.mountain3 {
  right: 0;
  width: 0;
  height: 0;
  border-left: 180rpx solid transparent;
  border-right: 180rpx solid transparent;
  border-bottom: 90rpx solid #0984e3;
}

/* 导航标签 */
.nav-tabs {
  display: flex;
  background: #fff;
  padding: 20rpx 32rpx;
  border-bottom: 1rpx solid #eee;
  overflow-x: auto;
}

.tab-item {
  padding: 16rpx 32rpx;
  font-size: 28rpx;
  color: #666;
  white-space: nowrap;
  margin-right: 24rpx;
}

.tab-item.active {
  color: #007aff;
  font-weight: bold;
  border-bottom: 2rpx solid #007aff;
}

/* 订单列表 */
.order-list {
  flex: 1;
  padding: 0 24rpx;
}

.order-card {
  background: #fff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.05);
}

.order-header {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
}

.student-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: 24rpx;
  background: #f0f0f0;
}

.order-info {
  flex: 1;
}

.student-name {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
}

.order-time {
  font-size: 24rpx;
  color: #999;
}

.order-status {
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
  font-size: 24rpx;
}

.order-status.pending {
  background: #fff3e0;
  color: #f57c00;
}

.order-status.accepted {
  background: #e3f2fd;
  color: #1976d2;
}

.order-status.completed {
  background: #e8f5e8;
  color: #388e3c;
}

.order-content {
  margin-bottom: 24rpx;
}

.order-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 12rpx;
}

.order-desc {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
  display: block;
  margin-bottom: 16rpx;
}

.order-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
}

.tag {
  background: #f0f0f0;
  color: #666;
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
  font-size: 24rpx;
}

.order-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.order-price {
  font-size: 28rpx;
  color: #f44336;
  font-weight: bold;
}

.order-actions {
  display: flex;
  gap: 16rpx;
}

.action-btn {
  padding: 12rpx 24rpx;
  border-radius: 12rpx;
  font-size: 26rpx;
}

.accept-btn {
  background: #007aff;
  color: #fff;
}

.contact-btn {
  background: #4caf50;
  color: #fff;
}

.completed-btn {
  background: #f0f0f0;
  color: #999;
}



/* 底部导航 */
.bottom-nav {
  display: flex;
  background: #fff;
  border-top: 1rpx solid #eee;
  padding: 16rpx 0;
}

.nav-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 8rpx 0;
}

.nav-item.active .nav-text {
  color: #007aff;
}

.nav-item.active .nav-icon {
  color: #007aff;
}

.nav-icon {
  font-size: 40rpx;
  margin-bottom: 4rpx;
}

.nav-text {
  font-size: 20rpx;
  color: #666;
}
</style>
