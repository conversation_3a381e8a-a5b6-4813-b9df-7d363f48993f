<template>
  <view class="order-detail">
    <!-- 顶部导航栏 -->
    <view class="nav-bar">
      <text class="back" @click="goBack">＜</text>
      <text class="nav-title">订单详情</text>
      <view class="nav-actions">
        <text class="nav-icon">⋯</text>
        <text class="nav-icon">⟲</text>
      </view>
    </view>

    <!-- 订单状态 -->
    <view class="status-section">
      <view class="status-badge" :class="getStatusClass(order.status)">
        <text class="status-text">{{ getStatusText(order.status) }}</text>
      </view>
    </view>

    <!-- 订单详情内容 -->
    <scroll-view class="content" scroll-y="true" :style="{ height: scrollHeight + 'px' }">
      <!-- 基本信息 -->
      <view class="info-section">
        <view class="section-title">
          <text class="title-text">订单信息</text>
        </view>
        
        <view class="info-item">
          <text class="info-label">订单号</text>
          <text class="info-value">{{ order.no || 'FD13455555333' }}</text>
        </view>

        <view class="info-item">
          <text class="info-label">发布时间</text>
          <text class="info-value">{{ order.date || '2025-3-12 12:11:12' }}</text>
        </view>

        <view class="info-item">
          <text class="info-label">辅导项目</text>
          <text class="info-value">{{ order.type || '作业' }}</text>
        </view>

        <view class="info-item">
          <text class="info-label">学习阶段</text>
          <text class="info-value">{{ order.stage || '本科' }}</text>
        </view>

        <view class="info-item">
          <text class="info-label">学习位置</text>
          <text class="info-value">{{ order.location || '国内' }}</text>
        </view>

        <view class="info-item">
          <text class="info-label">学校</text>
          <text class="info-value">{{ order.school || '北京大学' }}</text>
        </view>

        <view class="info-item">
          <text class="info-label">专业</text>
          <text class="info-value">{{ order.major || '数学' }}</text>
        </view>

        <view class="info-item">
          <text class="info-label">预算区间</text>
          <text class="info-value budget">{{ order.budget || '1000-2000元' }}</text>
        </view>
      </view>

      <!-- 学生信息 -->
      <view class="info-section">
        <view class="section-title">
          <text class="title-text">学生信息</text>
        </view>
        
        <view class="student-info">
          <view class="student-avatar">
            <text class="avatar-text">头像</text>
          </view>
          <view class="student-details">
            <text class="student-name">{{ order.studentName || '张同学' }}</text>
            <text class="student-desc">{{ order.stage || '本科' }} · {{ order.major || '数学' }}</text>
          </view>
        </view>
      </view>

      <!-- 需求描述 -->
      <view class="info-section">
        <view class="section-title">
          <text class="title-text">需求描述</text>
        </view>
        
        <view class="description-content">
          <text class="description-text">{{ order.description || '需要完成高等数学作业，包含微积分和线性代数部分' }}</text>
        </view>
      </view>

      <!-- 进度信息（仅交付中状态显示） -->
      <view class="info-section" v-if="order.status === 2">
        <view class="section-title">
          <text class="title-text">进度信息</text>
        </view>
        
        <view class="progress-item">
          <text class="progress-label">当前进度</text>
          <text class="progress-value">50%</text>
        </view>
        
        <view class="progress-item">
          <text class="progress-label">预计完成时间</text>
          <text class="progress-value">2025-3-20</text>
        </view>
      </view>
    </scroll-view>

    <!-- 底部操作按钮（仅交付中状态显示） -->
    <view class="bottom-action" v-if="order.status === 2">
      <button class="action-btn secondary" @click="contactStudent">联系学生</button>
      <button class="action-btn primary" @click="updateProgress">更新进度</button>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { onLoad } from '@dcloudio/uni-app'

const scrollHeight = ref(600)

// 订单信息
const order = ref({
  id: '',
  no: '',
  date: '',
  type: '',
  stage: '',
  location: '',
  school: '',
  major: '',
  budget: '',
  studentName: '',
  description: '',
  status: 0 // 0:投递中 1:已拒绝 2:交付中 3:已完成
})

// 返回上一页
const goBack = () => {
  uni.navigateBack()
}

// 获取状态文本
const getStatusText = (status) => {
  const statusMap = {
    0: '投递中',
    1: '已拒绝',
    2: '交付中',
    3: '已完成'
  }
  return statusMap[status] || '未知状态'
}

// 获取状态样式类
const getStatusClass = (status) => {
  const classMap = {
    0: 'pending',    // 投递中
    1: 'rejected',   // 已拒绝
    2: 'progress',   // 交付中
    3: 'completed'   // 已完成
  }
  return classMap[status] || 'pending'
}

// 联系学生
const contactStudent = () => {
  uni.showToast({
    title: '联系学生功能开发中',
    icon: 'none'
  })
}

// 更新进度
const updateProgress = () => {
  uni.showToast({
    title: '更新进度功能开发中',
    icon: 'none'
  })
}

onLoad((options) => {
  if (options && options.order) {
    // 接收传递的订单数据
    const orderData = JSON.parse(decodeURIComponent(options.order))
    order.value = { ...order.value, ...orderData }
  }
})

onMounted(() => {
  // 计算滚动区域高度
  const systemInfo = uni.getSystemInfoSync()
  scrollHeight.value = systemInfo.windowHeight - 250 // 减去导航栏和底部按钮高度
})
</script>

<style scoped>
.order-detail {
  min-height: calc(100vh - 120px);
  background: #f8f8f8;
  display: flex;
  flex-direction: column;
}

/* 顶部导航栏 */
.nav-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 32rpx;
  background: #fff;
  border-bottom: 1rpx solid #eee;
}

.back {
  font-size: 36rpx;
  color: #333;
  padding: 0 16rpx;
}

.nav-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  flex: 1;
  text-align: center;
}

.nav-actions {
  display: flex;
  gap: 16rpx;
}

.nav-icon {
  font-size: 32rpx;
  color: #666;
  padding: 0 8rpx;
}

/* 状态区域 */
.status-section {
  padding: 20rpx 32rpx;
  background: #fff;
  border-bottom: 1rpx solid #eee;
}

.status-badge {
  display: inline-block;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
}

.status-badge.pending {
  background: #fff3cd;
  color: #856404;
}

.status-badge.progress {
  background: #d1ecf1;
  color: #0c5460;
}

.status-badge.completed {
  background: #d4edda;
  color: #155724;
}

.status-badge.rejected {
  background: #f8d7da;
  color: #721c24;
}

/* 内容区域 */
.content {
  flex: 1;
  padding: 20rpx;
}

.info-section {
  background: #fff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 20rpx;
}

.section-title {
  margin-bottom: 24rpx;
  padding-bottom: 16rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.title-text {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 0;
  border-bottom: 1rpx solid #f8f8f8;
}

.info-item:last-child {
  border-bottom: none;
}

.info-label {
  font-size: 28rpx;
  color: #666;
}

.info-value {
  font-size: 28rpx;
  color: #333;
}

.info-value.budget {
  color: #ff6b35;
  font-weight: bold;
}

/* 学生信息 */
.student-info {
  display: flex;
  align-items: center;
  gap: 24rpx;
}

.student-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background: #f0f0f0;
  border: 2rpx solid #ddd;
  display: flex;
  align-items: center;
  justify-content: center;
}

.avatar-text {
  font-size: 20rpx;
  color: #999;
}

.student-details {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.student-name {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
}

.student-desc {
  font-size: 26rpx;
  color: #666;
}

/* 描述内容 */
.description-content {
  padding: 16rpx 0;
}

.description-text {
  font-size: 28rpx;
  color: #333;
  line-height: 1.6;
}

/* 进度信息 */
.progress-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 0;
  border-bottom: 1rpx solid #f8f8f8;
}

.progress-item:last-child {
  border-bottom: none;
}

.progress-label {
  font-size: 28rpx;
  color: #666;
}

.progress-value {
  font-size: 28rpx;
  color: #1e98d7;
  font-weight: bold;
}

/* 底部操作按钮 */
.bottom-action {
  display: flex;
  gap: 20rpx;
  padding: 20rpx 32rpx;
  background: #fff;
  border-top: 1rpx solid #eee;
}

.action-btn {
  flex: 1;
  height: 88rpx;
  border-radius: 44rpx;
  font-size: 32rpx;
  border: none;
}

.action-btn.secondary {
  background: #f8f8f8;
  color: #666;
}

.action-btn.primary {
  background: #1e98d7;
  color: #fff;
}
</style>
