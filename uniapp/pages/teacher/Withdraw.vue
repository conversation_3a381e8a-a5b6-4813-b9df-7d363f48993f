<template>
  <view class="withdraw">
    <!-- 顶部导航栏 -->
    <view class="nav-bar">
      <text class="back" @click="goBack">＜</text>
      <text class="nav-title">提现</text>
      <view class="nav-right">
        <text class="nav-dots">⋯</text>
        <text class="nav-help">?</text>
      </view>
    </view>
    
    <!-- 余额卡片 -->
    <view class="balance-card">
      <view class="balance-header">
        <text class="balance-title">未提现：{{ withdrawInfo.unpaid }}</text>
      </view>
      <view class="balance-details">
        <view class="balance-item">
          <text class="balance-label">冻结中：{{ withdrawInfo.frozen }}</text>
          <text class="balance-link" @click="viewFrozenOrders">查看冻结订单></text>
        </view>
        <view class="balance-item">
          <text class="balance-label">可提现：{{ withdrawInfo.available }}</text>
        </view>
      </view>
    </view>

    <!-- 提现表单 -->
    <view class="withdraw-form">
      <view class="form-item">
        <text class="form-label">提现金额</text>
        <input 
          class="form-input" 
          type="number" 
          v-model="withdrawAmount" 
          placeholder="请输入提现金额"
        />
      </view>
      
      <view class="form-item">
        <text class="form-label">实际到账</text>
        <text class="actual-amount">{{ actualAmount }}元</text>
        <text class="fee-note">实际到账=提现金额*0.9</text>
      </view>
    </view>

    <!-- 注意事项 -->
    <view class="notice-section">
      <text class="notice-title">注意：</text>
      <view class="notice-list">
        <text class="notice-item">1. 佣金费用=提现费用*10%</text>
        <text class="notice-item">2. 冻结中的订单需要在订单完成后14日之后方可提现。</text>
        <text class="notice-item">3. 提现一般5个工作日到账。</text>
      </view>
    </view>

    <!-- 提交按钮 -->
    <view class="submit-section">
      <button class="submit-btn" @click="submitWithdraw">确认提交</button>
    </view>
  </view>
</template>

<script setup>
import { ref, computed } from 'vue'

// 提现信息
const withdrawInfo = ref({
  unpaid: '1,000',
  frozen: '100',
  available: '900'
})

// 提现金额
const withdrawAmount = ref('')

// 计算实际到账金额
const actualAmount = computed(() => {
  const amount = parseFloat(withdrawAmount.value) || 0
  return (amount * 0.9).toFixed(0)
})

const goBack = () => {
  uni.navigateBack()
}

const viewFrozenOrders = () => {
  uni.navigateTo({
    url: './FrozenOrders'
  })
}

const submitWithdraw = () => {
  if (!withdrawAmount.value) {
    uni.showToast({
      title: '请输入提现金额',
      icon: 'none'
    })
    return
  }
  
  const amount = parseFloat(withdrawAmount.value)
  const available = parseFloat(withdrawInfo.value.available.replace(',', ''))
  
  if (amount > available) {
    uni.showToast({
      title: '提现金额不能超过可提现金额',
      icon: 'none'
    })
    return
  }
  
  uni.showModal({
    title: '确认提现',
    content: `确认提现${withdrawAmount.value}元？实际到账${actualAmount.value}元`,
    success: (res) => {
      if (res.confirm) {
        // 这里处理提现逻辑
        uni.showToast({
          title: '提现申请已提交',
          icon: 'success'
        })
        setTimeout(() => {
          uni.navigateBack()
        }, 1500)
      }
    }
  })
}
</script>

<style scoped>
.withdraw {
  min-height: calc(100vh - 120px);
  background: #f5f5f5;
}

/* 顶部导航栏 */
.nav-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 32rpx;
  background: #fff;
  border-bottom: 1rpx solid #eee;
}

.back {
  font-size: 40rpx;
  color: #333;
  padding: 0 16rpx;
}

.nav-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  flex: 1;
  text-align: center;
}

.nav-right {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.nav-dots {
  font-size: 32rpx;
  color: #333;
  padding: 0 8rpx;
}

.nav-help {
  font-size: 32rpx;
  color: #333;
  width: 40rpx;
  height: 40rpx;
  border: 2rpx solid #333;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  line-height: 1;
}

/* 余额卡片 */
.balance-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  margin: 32rpx;
  border-radius: 24rpx;
  padding: 48rpx 32rpx;
  color: #fff;
}

.balance-header {
  text-align: center;
  margin-bottom: 40rpx;
}

.balance-title {
  font-size: 48rpx;
  font-weight: bold;
}

.balance-details {
  display: flex;
  justify-content: space-between;
}

.balance-item {
  flex: 1;
  text-align: center;
}

.balance-label {
  font-size: 32rpx;
  display: block;
  margin-bottom: 8rpx;
}

.balance-link {
  font-size: 24rpx;
  text-decoration: underline;
  opacity: 0.8;
}

/* 提现表单 */
.withdraw-form {
  background: #fff;
  margin: 32rpx;
  border-radius: 16rpx;
  padding: 32rpx;
}

.form-item {
  margin-bottom: 32rpx;
}

.form-item:last-child {
  margin-bottom: 0;
}

.form-label {
  font-size: 32rpx;
  color: #333;
  margin-bottom: 16rpx;
  display: block;
}

.form-input {
  width: 100%;
  height: 80rpx;
  border: 2rpx solid #ddd;
  border-radius: 8rpx;
  padding: 0 24rpx;
  font-size: 32rpx;
  box-sizing: border-box;
}

.actual-amount {
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
  margin-right: 16rpx;
}

.fee-note {
  font-size: 24rpx;
  color: #ff6b6b;
}

/* 注意事项 */
.notice-section {
  margin: 32rpx;
}

.notice-title {
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 16rpx;
  display: block;
}

.notice-list {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.notice-item {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
}

/* 提交按钮 */
.submit-section {
  padding: 32rpx;
}

.submit-btn {
  width: 100%;
  height: 88rpx;
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  border: none;
  border-radius: 44rpx;
  color: #fff;
  font-size: 32rpx;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
