<template>
  <view class="wallet">
    <!-- 顶部导航栏 -->
    <view class="nav-bar">
      <text class="back" @click="goBack">＜</text>
      <text class="nav-title">我的钱包</text>
      <view class="nav-placeholder"></view>
    </view>
    
    <!-- 余额卡片 -->
    <view class="balance-card">
      <view class="balance-info">
        <text class="balance-label">账户余额</text>
        <text class="balance-amount">¥{{ walletInfo.balance }}</text>
      </view>
      <view class="balance-actions">
        <button class="action-btn recharge" @click="goRecharge">充值</button>
        <button class="action-btn withdraw" @click="goWithdraw">提现</button>
      </view>
    </view>
    
    <!-- 统计信息 -->
    <view class="stats-section">
      <view class="stat-item">
        <text class="stat-value">¥{{ walletInfo.totalSpent }}</text>
        <text class="stat-label">累计消费</text>
      </view>
      <view class="stat-item">
        <text class="stat-value">{{ walletInfo.orderCount }}</text>
        <text class="stat-label">订单数量</text>
      </view>
      <view class="stat-item">
        <text class="stat-value">¥{{ walletInfo.totalRecharge }}</text>
        <text class="stat-label">累计充值</text>
      </view>
    </view>
    
    <!-- 交易记录 -->
    <view class="records-section">
      <view class="section-header">
        <text class="section-title">交易记录</text>
        <text class="view-all" @click="viewAllRecords">查看全部</text>
      </view>
      
      <view class="record-list">
        <view class="record-item" v-for="(record, idx) in records" :key="idx">
          <view class="record-icon" :class="record.type">
            {{ getRecordIcon(record.type) }}
          </view>
          <view class="record-info">
            <text class="record-title">{{ record.title }}</text>
            <text class="record-time">{{ record.time }}</text>
          </view>
          <text class="record-amount" :class="record.type">
            {{ record.type === 'income' ? '+' : '-' }}¥{{ record.amount }}
          </text>
        </view>
      </view>
      
      <!-- 空状态 -->
      <view v-if="records.length === 0" class="empty-state">
        <text class="empty-text">暂无交易记录</text>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useUserStore } from '../../../store/user.js'

const userStore = useUserStore()

// 使用computed获取钱包信息
const walletInfo = computed(() => userStore.walletInfo)

const records = ref([
  {
    type: 'expense',
    title: '支付订单费用',
    time: '2025-01-15 14:30',
    amount: '1,200.00'
  },
  {
    type: 'income',
    title: '账户充值',
    time: '2025-01-14 10:20',
    amount: '2,000.00'
  },
  {
    type: 'expense',
    title: '支付订单费用',
    time: '2025-01-12 16:45',
    amount: '800.00'
  },
  {
    type: 'income',
    title: '退款到账',
    time: '2025-01-10 09:15',
    amount: '500.00'
  },
  {
    type: 'expense',
    title: '支付订单费用',
    time: '2025-01-08 11:30',
    amount: '1,500.00'
  }
])

const getRecordIcon = (type) => {
  return type === 'income' ? '💰' : '💸'
}

const goBack = () => {
  uni.navigateBack()
}

const goRecharge = () => {
  uni.navigateTo({ url: '/uniapp/pages/student/Recharge' })
}

const goWithdraw = () => {
  uni.navigateTo({ url: '/uniapp/pages/student/Withdraw' })
}

const viewAllRecords = () => {
  uni.navigateTo({ url: '/uniapp/pages/student/TransactionRecords' })
}
</script>

<style scoped>
.wallet {
  min-height: calc(100vh - 120px);
  background: #f5f5f5;
}

/* 顶部导航栏 */
.nav-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 32rpx;
  background: #fff;
  border-bottom: 1rpx solid #eee;
}

.back {
  font-size: 40rpx;
  color: #333;
  padding: 0 16rpx;
}

.nav-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  flex: 1;
  text-align: center;
}

.nav-placeholder {
  width: 48rpx;
  height: 48rpx;
}

/* 余额卡片 */
.balance-card {
  background: linear-gradient(135deg, #1e98d7 0%, #4fc3f7 100%);
  margin: 24rpx;
  padding: 40rpx;
  border-radius: 20rpx;
  color: #fff;
  box-shadow: 0 8rpx 24rpx rgba(30, 152, 215, 0.3);
}

.balance-info {
  margin-bottom: 32rpx;
}

.balance-label {
  font-size: 28rpx;
  opacity: 0.9;
  display: block;
  margin-bottom: 8rpx;
}

.balance-amount {
  font-size: 56rpx;
  font-weight: bold;
  display: block;
}

.balance-actions {
  display: flex;
  gap: 24rpx;
}

.action-btn {
  flex: 1;
  height: 72rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  background: rgba(255, 255, 255, 0.1);
  color: #fff;
}

.action-btn.recharge {
  background: rgba(255, 255, 255, 0.2);
}

/* 统计信息 */
.stats-section {
  display: flex;
  background: #fff;
  margin: 0 24rpx 24rpx 24rpx;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.05);
}

.stat-item {
  flex: 1;
  padding: 32rpx 16rpx;
  text-align: center;
  border-right: 1rpx solid #f0f0f0;
}

.stat-item:last-child {
  border-right: none;
}

.stat-value {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #666;
}

/* 交易记录 */
.records-section {
  background: #fff;
  margin: 0 24rpx 24rpx 24rpx;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.05);
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.section-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
}

.view-all {
  font-size: 26rpx;
  color: #1e98d7;
}

.record-list {
  padding: 0 32rpx;
}

.record-item {
  display: flex;
  align-items: center;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #f8f8f8;
}

.record-item:last-child {
  border-bottom: none;
}

.record-icon {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  margin-right: 24rpx;
}

.record-icon.income {
  background: #e8f5e8;
}

.record-icon.expense {
  background: #ffeaea;
}

.record-info {
  flex: 1;
}

.record-title {
  font-size: 28rpx;
  color: #333;
  display: block;
  margin-bottom: 4rpx;
}

.record-time {
  font-size: 24rpx;
  color: #999;
}

.record-amount {
  font-size: 30rpx;
  font-weight: bold;
}

.record-amount.income {
  color: #4caf50;
}

.record-amount.expense {
  color: #f44336;
}

/* 空状态 */
.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 80rpx 0;
}

.empty-text {
  color: #999;
  font-size: 28rpx;
}
</style>
