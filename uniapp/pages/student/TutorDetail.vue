<template>
  <view class="detail">
    <!-- 顶部返回栏 -->
    <view class="nav-bar">
      <text class="back" @click="goBack">＜</text>
      <text class="title">人员详情页面</text>
      <view class="nav-placeholder"></view>
    </view>
    <!-- 头部信息 -->
    <view class="header">
      <image :src="teacher.avatar || defaultAvatar" class="avatar" />
      <view class="header-info">
        <view class="name-row">
          <text class="name">{{ teacher.nickname }}</text>
          <text class="degree">{{ getEducationLevelText(teacher.educationLevel) }}</text>
          <text v-if="teacher.verified" class="verified">已实名认证</text>
        </view>
        <view class="school">{{ teacher.highestEducationSchool }} | {{ teacher.major }}</view>
        <view class="meta">
          <text>学员评分：{{ teacher.score || 0 }}分</text>
          <text class="divider">|</text>
          <text>辅导人数：{{ teacher.tutoringCount || 0 }}人</text>
        </view>
      </view>
    </view>
    <!-- 自述 -->
    <view class="section-title">自述</view>
    <view class="section-content">{{ teacher.selfIntroduction }}</view>
    <!-- 科研经历 -->
    <view class="section-title">科研经历</view>
    <view class="section-content">{{ teacher.experience || '暂无科研经历' }}</view>
    <!-- 可辅导 -->
    <view class="section-title">可辅导</view>
    <view class="tags">
      <button v-for="item in teacher.goodAtItems" :key="item" size="mini" class="tag-btn">{{ getGoodAtItemText(item) }}</button>
    </view>
    <!-- 用户评价 -->
    <view class="section-title">用户评价：</view>
    <scroll-view
        v-if="evaluationList && evaluationList.length"
        class="reviews"
        scroll-y="true"
        :style="{ height: scrollHeight + 'px' }"
        @scrolltolower="loadMore"
        :lower-threshold="100"
    >
      <view v-for="(review, idx) in evaluationList" :key="idx" class="review">
        <image :src="review.avatar || defaultAvatar" class="review-avatar" />
        <view class="review-info">
          <view class="review-header">
            <text class="review-name">{{ review.studentName }}</text>
            <text class="review-type">{{ constants.TUTORING_ITEM_TEXT[review.tutoringItem] }}</text>
            <text class="review-score">{{ review.score }}分</text>
            <text class="review-date">{{ review.evaluationDate }}</text>
          </view>
          <view class="review-content">{{ review.evaluation }}</view>
        </view>
      </view>
    </scroll-view>
    <view v-else class="no-review">暂无用户评价</view>
    <!-- 底部绿色按钮 -->
    <button v-if="teacher.showCopyWechatBtn" type="primary" class="contact-btn green" @click="copyWeixin">复制微信</button>
  </view>
</template>

<script setup>
import {ref, onMounted} from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import {constants, evaluationApi, teacherApi} from "../../../api";
import {handleApiResponse, handlePaginationData} from "../../../utils/apiHelper";

const defaultAvatar = '/static/logo.png'

const teacher = ref({})
const loading = ref(false)

const scrollHeight = ref(600)
const loadingMore = ref(false)
const evaluationList = ref([])
const pagination = ref({
  current: 1,
  size: 10,
  total: 0,
  hasMore: true
})

const goBack = () => {
  uni.navigateBack()
}

function copyWeixin() {
  uni.setClipboardData({
    data: teacher.value.wechatNo,
    success: () => {
      uni.showToast({ title: '已复制成功，可以添加微信，备注：来自辅导君', icon: 'success' })
    }
  })
}

// 获取学历文本
const getEducationLevelText = (level) => {
  return constants.TEACHER_LEVEL_TEXT[level] || '未知'
}

// 获取擅长项目文本
const getGoodAtItemText = (item) => {
  return constants.GOOD_AT_ITEMS_TEXT[item]
}

onLoad((options) => {
  if (options && options.teacherId) {
    loadTeacherDetail(options.teacherId)

    // 加载评价列表
    loadEvaluationList(options.teacherId)
  }
})

const loadTeacherDetail = async (teacherId) => {
  if (!teacherId) return

  loading.value = true
  try {
    const response = await teacherApi.getTeacherDetail(teacherId)

    if (response.code === '1000') {
      teacher.value = response.result || {}
    } else {
      uni.showToast({
        title: response.message || '加载失败',
        icon: 'error'
      })
    }
  } catch (error) {
    console.error('加载教师详情失败:', error)
    uni.showToast({
      title: '网络错误',
      icon: 'error'
    })
  } finally {
    loading.value = false
  }
}

const loadEvaluationList = async (teacherId) => {
  if (!teacherId) return

  loading.value = true
  loadingMore.value = true

  const params = {
    current: pagination.value.current,
    size: pagination.value.size,
    teacherId: teacherId
  }

  const result = await handleApiResponse(
      evaluationApi.getEvaluationList(params),
      {
        showLoading: false,
        showError: true
      }
  )

  loading.value = false
  loadingMore.value = false

  if (result.success) {
    const paginationData = handlePaginationData(
        { result: result.data },
        evaluationList.value,
        false
    )

    evaluationList.value = paginationData.list
    pagination.value = paginationData.pagination
  }

}

// 加载更多数据
const loadMore = async () => {
  if (!pagination.value.hasMore || loadingMore.value) return

  pagination.value.current += 1
  await loadEvaluationList()
}

onMounted(() => {
  // 计算滚动区域高度
  const systemInfo = uni.getSystemInfoSync()
  scrollHeight.value = systemInfo.windowHeight - 240 // 减去导航栏、标签页和tabbar高度
})
</script>

<style scoped>
.detail {
  background: #f5f5f5;
  min-height: calc(100vh - 120px);
}

/* 顶部导航栏 */
.nav-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 32rpx;
  background: #fff;
  border-bottom: 1rpx solid #eee;
}

.back {
  font-size: 40rpx;
  color: #333;
  padding: 0 16rpx;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  flex: 1;
  text-align: center;
  color: #333;
}

.nav-placeholder {
  width: 48rpx;
  height: 48rpx;
}

/* 头部信息 */
.header {
  display: flex;
  align-items: center;
  background: #fff;
  margin: 24rpx;
  padding: 32rpx;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.05);
}

.avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  margin-right: 24rpx;
  background: #eee;
}

.header-info {
  flex: 1;
}

.name-row {
  display: flex;
  align-items: center;
  gap: 12rpx;
  margin-bottom: 12rpx;
}

.name {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.degree {
  font-size: 26rpx;
  color: #888;
  margin-left: 8rpx;
}

.verified {
  background: #ffe0a3;
  color: #b97a00;
  font-size: 22rpx;
  border-radius: 8rpx;
  padding: 4rpx 12rpx;
  margin-left: 8rpx;
}

.school {
  color: #666;
  font-size: 28rpx;
  margin-bottom: 12rpx;
}

.meta {
  color: #999;
  font-size: 24rpx;
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.divider {
  margin: 0 8rpx;
}

/* 内容区域 */
.section-title {
  font-weight: bold;
  font-size: 30rpx;
  margin: 32rpx 24rpx 16rpx 24rpx;
  border-left: 6rpx solid #1e98d7;
  padding-left: 16rpx;
  color: #333;
}

.section-content {
  font-size: 28rpx;
  color: #444;
  background: #fff;
  margin: 0 24rpx 24rpx 24rpx;
  padding: 24rpx;
  border-radius: 12rpx;
  line-height: 1.5;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.05);
}

.tags {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  background: #fff;
  margin: 0 24rpx 24rpx 24rpx;
  padding: 24rpx;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.05);
}

.tag-btn {
  background: #e3f2fd;
  color: #1976d2;
  border: none;
  font-size: 24rpx;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
}

/* 评价区域 */
.reviews {
  background: #fff;
  margin: 0 24rpx 24rpx 24rpx;
  padding: 24rpx;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.05);
}

.review {
  display: flex;
  align-items: flex-start;
  margin-bottom: 24rpx;
  background: #fafafa;
  border-radius: 12rpx;
  padding: 20rpx;
}

.review:last-child {
  margin-bottom: 0;
}

.review-avatar {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  margin-right: 16rpx;
  background: #eee;
}

.review-info {
  flex: 1;
}

.review-header {
  display: flex;
  align-items: center;
  gap: 12rpx;
  font-size: 24rpx;
  margin-bottom: 8rpx;
}

.review-name {
  font-weight: bold;
  color: #333;
}

.review-type {
  color: #1976d2;
}

.review-score {
  color: #ff9800;
}

.review-date {
  color: #bbb;
  font-size: 22rpx;
}

.review-content {
  color: #444;
  font-size: 26rpx;
  line-height: 1.4;
}

.no-review {
  color: #bbb;
  font-size: 28rpx;
  text-align: center;
  margin: 40rpx 0;
}

/* 联系按钮 */
.contact-btn {
  width: calc(100% - 48rpx);
  margin: 32rpx 24rpx 40rpx 24rpx;
  font-size: 32rpx;
  height: 96rpx;
  border-radius: 12rpx;
  background: #1aad19;
  color: #fff;
  border: none;
  font-weight: bold;
}
</style>