<template>
  <view class="mine">
    <!-- 用户信息区域 -->
    <view class="user-section">
      <!-- 头像 -->
      <view class="avatar-container">
        <image class="user-avatar" :src="userInfo.avatar || '/static/default-avatar.png'" mode="aspectFill" />
      </view>

      <!-- 用户名和状态 -->
      <view class="user-info">
        <text class="user-name">{{ userInfo.nickname || 'FIGHTING' }}</text>
        <view class="user-status">
          <text v-if="isVerified" class="status-tag verified">学生角色</text>
          <text v-else class="status-tag unverified" @click="goAuth">认证辅导老师></text>
        </view>
      </view>
    </view>

    <!-- 功能菜单 -->
    <view class="menu-section">
      <view class="menu-item" @click="goOrders">
        <text class="menu-text">我的订单</text>
        <text class="menu-arrow">></text>
      </view>

      <view class="menu-item" @click="goCustomerService">
        <text class="menu-text">在线客服</text>
        <text class="menu-arrow">></text>
      </view>
    </view>
  </view>
</template>

<script setup>
import { computed, onMounted } from 'vue'
import { useUserStore } from '../../../store/user.js'
import {USER_ROLE} from "../../../api/constants";

const userStore = useUserStore()

// 使用computed获取用户信息，确保响应式
const userInfo = computed(() => userStore.userInfo)
const isVerified = computed(() => userStore.isVerified)

onMounted(async () => {
  // 页面加载时恢复用户信息
  userStore.restoreUserInfo()

  if (!userStore.isLoggedIn) {
      uni.navigateTo({url: '/uniapp/pages/Login'})

    // 不是学生角色，需要跳转
    if (userStore.userInfo.role !== USER_ROLE.STUDENT) {
      uni.navigateTo({url: '/uniapp/pages/teacher/Home'})
    }
  }
})

const goOrders = () => {
  uni.navigateTo({ url: './Orders' })
}

const goAuth = () => {
  uni.navigateTo({ url: './Auth' })
}

const goCustomerService = () => {
  // 可以跳转到客服页面或者打开客服聊天
  uni.navigateTo({ url: './CustomerService' })
}


</script>

<style scoped>
.mine {
  min-height: calc(100vh - 120px);
  background: #f8f8f8;
  padding: 40rpx 32rpx;
}

/* 用户信息区域 */
.user-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 60rpx 0;
  background: #fff;
  border-radius: 16rpx;
  margin-bottom: 40rpx;
}

.avatar-container {
  margin-bottom: 32rpx;
}

.user-avatar {
  width: 160rpx;
  height: 160rpx;
  border-radius: 50%;
  background: #f0f0f0;
  border: 4rpx solid #e0e0e0;
}

.user-info {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.user-name {
  font-size: 40rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 16rpx;
}

.user-status {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.status-tag {
  font-size: 26rpx;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
}

.verified {
  background: #e3f2fd;
  color: #1976d2;
}

.unverified {
  background: transparent;
  color: #f44336;
  text-decoration: underline;
}

/* 功能菜单 */
.menu-section {
  background: #fff;
  border-radius: 16rpx;
  overflow: hidden;
}

.menu-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 40rpx 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-text {
  font-size: 32rpx;
  color: #333;
}

.menu-arrow {
  color: #999;
  font-size: 28rpx;
}
</style>
