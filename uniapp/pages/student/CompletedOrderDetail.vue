<template>
  <view class="order-detail">
    <!-- 顶部导航栏 -->
    <view class="nav-bar">
      <text class="back" @click="goBack">＜</text>
      <text class="nav-title">订单详情</text>
      <view class="nav-actions">
        <text class="nav-icon">⋯</text>
        <text class="nav-icon">⟲</text>
      </view>
    </view>

    <!-- 订单号 -->
    <view class="order-number-section">
      <view class="order-number">
        <text class="order-label">订单号</text>
        <text class="order-value">{{ order.no || 'JS13455555333' }}</text>
      </view>
    </view>

    <!-- 订单信息列表 -->
    <view class="order-info-list">
      <view class="info-row">
        <text class="info-label">辅导项目</text>
        <text class="info-value">{{ order.type || '作业' }}</text>
      </view>

      <view class="info-row">
        <text class="info-label">辅导老师</text>
        <text class="info-value teacher-name" @click="goToTeacherDetail">{{ currentTeacher.name || '张老师' }}></text>
      </view>

      <!-- 交付阶段区域 -->
      <view class="delivery-section">
        <view class="delivery-header">
          <text class="delivery-label">交付阶段</text>
          <text class="delivery-progress">{{ currentStage }}/5</text>
        </view>



        <!-- 阶段列表 -->
        <view class="stage-list">
          <view class="stage-item" v-for="(stage, index) in deliveryStages" :key="index">
            <text class="stage-label">第{{ index + 1 }}阶段</text>
            <view class="stage-status" :class="stage.statusClass">
              <text class="stage-status-text">{{ stage.status }}</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 评价 -->
      <view class="info-row">
        <text class="info-label">评价</text>
        <text class="info-value rating-value">{{ evaluation.rating || '4.5' }}分</text>
      </view>
    </view>

    <!-- 评价详情 -->
    <view class="evaluation-detail">
      <view class="evaluation-scores">
        <view class="score-item">
          <text class="score-label">专业度</text>
        </view>
        <view class="score-item">
          <text class="score-label">服务度</text>
        </view>
      </view>

      <view class="evaluation-comment">
        <text class="comment-text">{{ evaluation.comment || '老师认真负责，辅导的时候很用心。老师认真负责，辅导的时候很用心老师认真负责，辅导的时候很用心老师' }}</text>
      </view>
    </view>

  </view>
</template>

<script setup>
import { ref, computed } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { useOrderStore } from '../../../store/order.js'

const orderStore = useOrderStore()
const orderId = ref('')

// 使用computed获取当前订单信息
const order = computed(() => {
  if (orderId.value) {
    return orderStore.getOrderById(orderId.value) || {}
  }
  return orderStore.currentOrder || {}
})

// 当前辅导老师信息
const currentTeacher = ref({
  name: '张老师',
  avatar: '/static/logo.png',
  education: '牛津大学 · 英语文学 · 博士',
  experience: '8年英语论文指导经验，专注于学术写作和语法修正',
  tags: ['英语专业', '海外博士', '论文专家'],
  completedOrders: 45,
  rating: '4.8',
  responseTime: '1小时'
})

// 交付阶段数据
const deliveryStages = ref([
  { status: '待付款：1000', statusClass: 'pending-payment' },
  { status: '已付待交付', statusClass: 'paid-waiting' },
  { status: '已交付待验收', statusClass: 'delivered-waiting' },
  { status: '已验收', statusClass: 'accepted' },
  { status: '已验收', statusClass: 'accepted' }
])

// 当前阶段
const currentStage = ref(1)

// 评价信息 - 从订单数据中获取
const evaluation = computed(() => {
  const orderData = order.value
  if (orderData && orderData.evaluation) {
    return {
      rating: orderData.evaluation.overallRating || 4.5,
      comment: orderData.evaluation.comment || '老师认真负责，辅导的时候很用心。老师认真负责，辅导的时候很用心老师认真负责，辅导的时候很用心老师',
      tags: ['专业负责', '沟通顺畅', '交付及时', '质量很高'],
      time: orderData.evaluation.date || '2025-3-19 10:30:00'
    }
  }
  return {
    rating: 4.5,
    comment: '老师认真负责，辅导的时候很用心。老师认真负责，辅导的时候很用心老师认真负责，辅导的时候很用心老师',
    tags: ['专业负责', '沟通顺畅', '交付及时', '质量很高'],
    time: '2025-3-19 10:30:00'
  }
})

const goBack = () => {
  uni.navigateBack()
}

// 跳转到老师详情页面
const goToTeacherDetail = () => {
  uni.navigateTo({
    url: '/uniapp/pages/student/TeacherDetail?teacherId=' + currentTeacher.value.id
  })
}

onLoad((options) => {
  if (options && options.orderId) {
    orderId.value = options.orderId
  }
})
</script>

<style scoped>
.order-detail {
  min-height: calc(100vh - 120px);
  background: #f8f8f8;
  display: flex;
  flex-direction: column;
}

/* 顶部导航栏 */
.nav-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 32rpx;
  background: #fff;
  border-bottom: 1rpx solid #eee;
}

.back {
  font-size: 36rpx;
  color: #333;
  padding: 0 16rpx;
}

.nav-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  flex: 1;
  text-align: center;
}

.nav-actions {
  display: flex;
  gap: 16rpx;
}

.nav-icon {
  font-size: 36rpx;
  color: #333;
  padding: 0 8rpx;
}

/* 订单号区域 */
.order-number-section {
  background: #fff;
  padding: 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.order-number {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.order-label {
  font-size: 28rpx;
  color: #333;
}

.order-value {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
}

/* 订单信息列表 */
.order-info-list {
  background: #fff;
  margin-top: 16rpx;
}

.info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.info-label {
  font-size: 28rpx;
  color: #333;
}

.info-value {
  font-size: 28rpx;
  color: #666;
}

.teacher-name {
  color: #1e98d7;
}

.rating-value {
  color: #333;
  font-weight: bold;
}

/* 交付阶段区域 */
.delivery-section {
  padding: 0 32rpx 32rpx 32rpx;
  position: relative;
}

.delivery-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.delivery-label {
  font-size: 28rpx;
  color: #333;
}

.delivery-progress {
  font-size: 28rpx;
  color: #666;
}



/* 阶段列表 */
.stage-list {
  margin-top: 16rpx;
}

.stage-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.stage-item:last-child {
  border-bottom: none;
}

.stage-label {
  font-size: 28rpx;
  color: #333;
}

.stage-status {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
}

.stage-status-text {
  color: #fff;
}

.pending-payment {
  background: #1e98d7;
}

.paid-waiting {
  background: #999;
}

.delivered-waiting {
  background: #1e98d7;
}

.accepted {
  background: #999;
}

/* 评价详情 */
.evaluation-detail {
  background: #fff;
  margin-top: 16rpx;
  padding: 32rpx;
}

.evaluation-scores {
  display: flex;
  gap: 32rpx;
  margin-bottom: 32rpx;
}

.score-item {
  flex: 1;
}

.score-label {
  font-size: 28rpx;
  color: #333;
}

.evaluation-comment {
  border-top: 1rpx solid #f0f0f0;
  padding-top: 32rpx;
}

.comment-text {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
}

</style>
